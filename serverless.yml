service: orders-scheduler
frameworkVersion: "3.33.0" # Specify exact version
plugins:
  - serverless-dotenv-plugin
  - serverless-esbuild
  - serverless-offline
provider:
  name: aws
  runtime: nodejs18.x # Update to match your Node.js version
  region: us-east-1
  stage: ${opt:stage, 'dev'}
  environment:
    ORDER_API_URL: ${env:ORDER_API_URL}
    SENDGRID_API_KEY: ${env:SENDGRID_API_KEY}
    TEMPLATE_ID: ${env:TEMPLATE_ID}
    SENDER_EMAIL: ${env:SENDER_EMAIL}
    BUSINESS_INFO_API_URL: ${env:BUSINESS_INFO_API_URL}
    TOKEN_API_URL: ${env:TOKEN_API_URL}
    PAYMENT_API_URL: ${env:PAYMENT_API_URL}

    DYNAMODB_CUSTOMER_TABLE: ${self:service}-customerTable-${sls:stage}
    DYNAMODB_SCHEDULE_TABLE: ${self:service}-schedulesTable-${sls:stage}
    DYNAMODB_ORDER_TABLE: ${self:service}-orderTable-${sls:stage}
    DYNAMODB_AUTOINCREMENT_TABLE: ${self:service}-autoIncrementTable-${sls:stage}
  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - "dynamodb:PutItem"
        - "dynamodb:Query"
      Resource: arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/${self:service}-orderTable-${sls:stage}
    - Effect: "Allow"
      Action:
        - "dynamodb:PutItem"
        - "dynamodb:DescribeStream"
        - "dynamodb:GetRecords"
        - "dynamodb:GetShardIterator"
        - "dynamodb:ListStreams"
        - "dynamodb:DeleteItem"
        - "dynamodb:UpdateItem"
        - "dynamodb:Scan"
        - "dynamodb:Query"
        - "dynamodb:GetItem"
      Resource: arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/${self:service}-schedulesTable-${sls:stage}
    - Effect: "Allow"
      Action:
        - "dynamodb:DescribeTable"
        - "dynamodb:CreateTable"
        - "dynamodb:PutItem"
        - "dynamodb:DeleteItem"
        - "dynamodb:UpdateItem"
        - "dynamodb:Query"
        - "dynamodb:GetItem"
      Resource: arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/${self:service}-autoIncrementTable-${sls:stage}
    - Effect: "Allow"
      Action:
        - "dynamodb:Query"
        - "dynamodb:GetItem"
      Resource: arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/${self:service}-schedulesTable-${sls:stage}/index/businessId-scheduleId-index
custom:
  dotenv:
    path: .env.${opt:stage, 'dev'}
functions:
  createSchedule:
    handler: src/createSchedule.handler
    events:
      - http:
          path: /
          method: post
          cors: true

  fetchSchedules:
    handler: src/fetchSchedules.fetchSchedules
    events:
      - http:
          path: /business/{businessId}/subscriptions
          method: get
          cors: true
  fetchUserSpecificSchedules:
    handler: src/fetchSchedules.fetchUserSpecificSchedules
    events:
      - http:
          path: /business/{businessId}/subscriptions/{userId}
          method: get
          cors: true

  streamOrder:
    handler: src/streamOrder.handler
    description: Deactivate license upon Paddle event
    events:
      - stream:
          type: dynamodb
          arn: !GetAtt OrderTable.StreamArn
          maximumRetryAttempts: 1
          batchSize: 5
          filterPatterns:
            - eventName: [REMOVE]

  scheduleOrder:
    handler: src/scheduleOrder.handler
    events:
      - http:
          path: /schedule-order
          method: post
          cors: true

  streamSchedule:
    handler: src/streamSchedule.handler
    description: Handle schedule upon DynamoDB stream event
    events:
      - stream:
          type: dynamodb
          arn: !GetAtt SchedulesTable.StreamArn
          maximumRetryAttempts: 1
          batchSize: 5
          filterPatterns:
            - eventName: [REMOVE]

  unsubscribeSchedule:
    handler: src/unsubscribeSchedule.handler
    events:
      - http:
          path: /unsubscribe-schedule
          method: post
          cors: true

  updateSchedule:
    handler: src/updateSchedule.handler
    events:
      - http:
          path: /update-schedule
          method: post
          cors: true

resources:
  Resources:
    OrderTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: orderId
            AttributeType: S
        KeySchema:
          - AttributeName: orderId
            KeyType: HASH
        TimeToLiveSpecification: # ttl definition
          AttributeName: ttl
          Enabled: true
        TableName: ${self:service}-orderTable-${sls:stage}
        ProvisionedThroughput:
          ReadCapacityUnits: 1
          WriteCapacityUnits: 1
        StreamSpecification:
          StreamViewType: OLD_IMAGE

    SchedulesTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: scheduleId
            AttributeType: S
        KeySchema:
          - AttributeName: scheduleId
            KeyType: HASH
        TimeToLiveSpecification:
          AttributeName: ttl
          Enabled: true
        TableName: ${self:service}-schedulesTable-${sls:stage}
        ProvisionedThroughput:
          ReadCapacityUnits: 1
          WriteCapacityUnits: 1
        StreamSpecification:
          StreamViewType: OLD_IMAGE
