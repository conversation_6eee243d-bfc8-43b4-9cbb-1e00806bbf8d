const axios = require('axios');
const AWS = require('aws-sdk');
// Create a DynamoDB instance
const dynamoDB = new AWS.DynamoDB.DocumentClient();
const notificationUrl = "https://beta1.tossdown.com/api/send_msg_notification";
exports.handler = async (event) => {
    try {
      const postOrders = [];
      let order;
      let orderId;
      let data ;
      const records = event.Records;
      console.log(JSON.stringify(records));
      for (const record of records) {
        if (record.eventName === 'REMOVE') {
            order = AWS.DynamoDB.Converter.unmarshall(record.dynamodb.OldImage);
             data = JSON.stringify({
              orderId: order.orderId,
              cluster: order.cluster,
              order_status: order.order_status,
              business_id: order.business_id,
              type: order.type
            });
            console.log(`Deleted order ID: ${orderId}`);
            postOrders.push(axios.post(notificationUrl, data, {headers:{"Content-Type" : "application/json"}}));
        }
    }
    await Promise.all(postOrders);
      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Event processed successfully' }),
      };
    } catch (error) {
      console.error("Error processing event:", error);
      return {
        statusCode: 500,
        body: JSON.stringify({ error: 'Internal Server Error' }),
      };
    }
  };