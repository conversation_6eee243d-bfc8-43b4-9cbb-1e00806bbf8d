require("dotenv").config();
const sgMail = require("@sendgrid/mail");
const axios = require("axios");
sgMail.setApiKey(process.env.SENDGRID_API_KEY);
const templateId = process.env.TEMPLATE_ID;
const senderEmail = process.env.SENDER_EMAIL;
const UNSUB_URL = process.env.UNSUB_URL;

const BUSINESS_INFO_API_URL = process.env.BUSINESS_INFO_API_URL;
function formatDateToAMPM(dateStr) {
  // Create a date object from the input string
  const date = new Date(dateStr);
  
  // Options for formatting the date and time in Canada/Eastern timezone
  const options = {
    timeZone: 'America/Toronto',
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  };

  return date.toLocaleString('en-US', options)
    .replace(',', '')  // Remove the comma between date and time
    .replace(' at', ''); // Remove 'at' if present
}

// Add a helper function to check if a value is zero or null
const isZeroOrNull = (value) => {
  return !value || Number(value) === 0;
};

// Add this helper function at the top of the file
const isZeroOrEmpty = (value) => {
  if (value === null || value === undefined || value === '') return true;
  const numValue = Number(value);
  return isNaN(numValue) || numValue === 0 || numValue === 0.00;
};

/**
 * Sends an email using the provided order details.
 *
 * @param {string} recipientEmail - The recipient's email address.
 * @param {string} senderEmail - The sender's email address.
 * @param {string} templateId - The SendGrid template ID.
 * @param {object} orderDetails - The order details object.
 */
exports.sendSubscriptionConfirmationEmail = async (
  orderDetails,
  businessId,
  scheduleId,
  subscriptionTime
) => {
  console.log(">>>>>orderDetails>>>>>", orderDetails);
  const response = await axios.get(
    `${BUSINESS_INFO_API_URL}/${businessId}/locations`
  );
  console.log(`${BUSINESS_INFO_API_URL}/${businessId}`);
  const businessInfo = response.data.result;
  console.log(">>Business info>>>>", businessInfo?.email, businessInfo);
  const date = new Date(subscriptionTime);
  const decimalPlaces = businessInfo?.decimal_places;
  const roundToDecimal = (num) => Number(num).toFixed(decimalPlaces);
  // Format the date to match the required output
  const formattedDate = orderDetails?.delivery.toString().split(" ").slice(0, 5).join(" ");
  const orderDate = date.toString().split(" ").slice(0, 5).join(" ");
  console.log("fomated>>", formattedDate, "sssssss", subscriptionTime);
  const msg = {
    to: orderDetails.email,
    from: businessInfo?.email,
    templateId: templateId,
    dynamic_template_data: {
      total: roundToDecimal(orderDetails.gtotal),
      restaurant_logo: businessInfo.logo,
      cancel_subscription: 1,
      restaurant_contact: businessInfo.contact_phone,
      restaurant_email: businessInfo.email,
      unsubscribe_link: `https://ezeats.tossdown.website/unsubscribe?scheduleId=${scheduleId}`,
      schedule_id: scheduleId,
      user: businessInfo.name,
      name: orderDetails.name,
      phone: orderDetails.mobile_phone,
      title: "Your subscription is successful",
      subject: "You have subscribed successfully.",
      email_icon:
        "https://tossdown.site/images/532cd57816e002c5bae77e9b69abab99_1630326342.png",
      address: orderDetails.address,
      paid: false,
      order_discount_check: !isZeroOrEmpty(orderDetails?.discount),
      order_tip_check: !isZeroOrEmpty(orderDetails?.tip),
      order_service_charges_check: !isZeroOrEmpty(orderDetails?.service_charges),
      order_tax_check: !isZeroOrEmpty(orderDetails?.tax_value),
      details_check: true,
      button_bg: "#E5EEFB",
      button_text: "#0057D9",
      return_policy: false,
      order_total: roundToDecimal(orderDetails.gtotal),
      order_weight_check: !isZeroOrEmpty(orderDetails?.weight_value),
      order_weight: !isZeroOrEmpty(orderDetails?.weight_value) ? 
        `${roundToDecimal(orderDetails?.weight_value)} ${orderDetails?.weight_unit}` : undefined,
      order_type: orderDetails.ordertype,
      discount: !isZeroOrEmpty(orderDetails?.discount) ? roundToDecimal(orderDetails?.discount) : undefined,
      discount_percent: !isZeroOrEmpty(orderDetails?.discount) ? roundToDecimal(orderDetails?.discount) : undefined,
      tax_percent: !isZeroOrEmpty(orderDetails?.tax) ? `${roundToDecimal(orderDetails?.tax)}%` : undefined,
      customer_note: orderDetails.note,
      tax_amount: !isZeroOrEmpty(orderDetails?.tax_value) ? roundToDecimal(orderDetails?.tax_value) : undefined,
      item_count: orderDetails.items.length,
      website: businessInfo.name,
      order_no: orderDetails.orderid,
      currency: businessInfo.currencycode,
      delivery_charges_check: !isZeroOrEmpty(orderDetails?.delivery_charges),
      delivery_charges: !isZeroOrEmpty(orderDetails?.delivery_charges) ? 
        roundToDecimal(orderDetails?.delivery_charges) : undefined,
      tip: !isZeroOrEmpty(orderDetails?.tip) ? roundToDecimal(orderDetails?.tip) : undefined,
      service_charges: !isZeroOrEmpty(orderDetails?.service_charges) ? 
        roundToDecimal(orderDetails?.service_charges) : undefined,
      sub_total: roundToDecimal(orderDetails.total),
      order_time: "Date: " + formatDateToAMPM(date),
      order_date: orderDetails?.delivery ? 
        "Date: " + formatDateToAMPM(orderDetails.delivery) : 
        undefined,
      subscription_id: scheduleId,
      order_text: "Recieved",
      items: (orderDetails.items || []).map((item) => ({
        text: item.name,
        price: roundToDecimal(item.price),
        count: item.qty,
        discount_check: !isZeroOrEmpty(item?.discount),
        discount_value: !isZeroOrEmpty(item?.item_level_discount_value) ? 
          roundToDecimal(item?.item_level_discount_value) : undefined,
        tax_check: !isZeroOrEmpty(item?.tax),
        tax: !isZeroOrEmpty(item?.tax) ? roundToDecimal(item?.tax) : undefined,
        tax_amount: !isZeroOrEmpty(item?.item_level_tax_value) ? 
          roundToDecimal(item?.item_level_tax_value) : undefined,
        weight_check: !isZeroOrEmpty(item?.weight_value),
        weight: !isZeroOrEmpty(item?.weight_value) ? 
          `${roundToDecimal(item?.weight_value)} ${item?.weight_unit}` : undefined,
        orignal_price: roundToDecimal(item.price),
        currency: businessInfo.currencycode,
      })),
    },
  };

  console.log("Bizzzzzzzz>>>>>", BUSINESS_INFO_API_URL);

  console.log("Email Mesgeeeee>>", msg);
  console.log("Ordr itemssss>>", msg?.items);

  // console.log(businessInfo);

  try {
    await sgMail.send(msg);
    console.log("Email sent");
  } catch (error) {
    console.error("Error sending email:", error);
    throw new Error("Email sending failed");
  }
};

exports.sendSubscriptionCancellationEmail = async (
  orderDetails,
  businessId,
  scheduleId,
  cancelationReason
) => {
  try {
    // Log input parameters for debugging
    console.log('>>>>>orderDetails>>>>>', orderDetails);
    console.log('Sending cancellation email with params:', {
      orderDetails: orderDetails,
      businessId: businessId,
      scheduleId: scheduleId,
      cancelationReason: cancelationReason
    });
    const date = new Date();
    // Format the date to match the required output
    const orderDate = date.toString().split(" ").slice(0, 5).join(" ");
    const response = await axios.get(
      `${BUSINESS_INFO_API_URL}/${businessId}/locations`
    );
    
    // Log API response
    console.log('Business API response:', response.data);

    const businessInfo = response.data.result;
    if (!businessInfo) {
      throw new Error('Business info not found');
    }

    const decimalPlaces = businessInfo?.decimal_places || 2;
    const roundToDecimal = (num) => Number(num || 0).toFixed(decimalPlaces);

    // Validate required email fields
    if (!orderDetails?.email) {
      throw new Error('Recipient email is missing');
    }
    if (!businessInfo?.email) {
      throw new Error('Sender email is missing');
    }

    // Helper function to format date to AM/PM
    function formatDateToAMPM(dateStr) {
      const date = new Date(dateStr);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        hour12: true
      });
    }

    const msg = {
      to: orderDetails.email,
      from: businessInfo.email,
      templateId: templateId,
      dynamic_template_data: {
        total: roundToDecimal(orderDetails?.gtotal),
        restaurant_logo: businessInfo.logo || '',
        restaurant_contact: businessInfo.contact_phone || '',
        restaurant_email: businessInfo.email,
        unsubscribe_link: `https://ezeats.tossdown.website/unsubscribe?scheduleId=${scheduleId}`,
        schedule_id: scheduleId,
        user: businessInfo.name || '',
        name: orderDetails?.name || '',
        phone: orderDetails?.mobile_phone || '',
        title: "Your subscription has been cancelled",
        subject: "You have unsubscribed successfully.",
        cancelation_reason: cancelationReason || 'Not specified',
        email_icon: "https://tossdown.site/images/532cd57816e002c5bae77e9b69abab99_1630326342.png",
        address: orderDetails?.address || '',
        paid: false,
        order_discount_check: (orderDetails?.discount || 0) == 0 ? false : true,
        order_tip_check: (orderDetails?.tip || 0) == 0 ? false : true,
        order_service_charges_check: (orderDetails?.service_charges || 0) == 0 ? false : true,
        order_tax_check: true,
        details_check: true,
        button_bg: "#E5EEFB",
        button_text: "#0057D9",
        return_policy: false,
        order_total: roundToDecimal(orderDetails?.gtotal),
        order_weight: `${roundToDecimal(orderDetails?.weight_value)} ${orderDetails?.weight_unit || ''}`,
        order_type: orderDetails?.ordertype || '',
        discount: roundToDecimal(orderDetails?.discount),
        discount_percent: roundToDecimal(orderDetails?.discount),
        tax_percent: !isZeroOrEmpty(orderDetails?.tax) ? `${roundToDecimal(orderDetails?.tax)}%` : undefined,
        customer_note: orderDetails?.note || '',
        tax_amount: !isZeroOrEmpty(orderDetails?.tax_value) ? roundToDecimal(orderDetails?.tax_value) : undefined,
        item_count: orderDetails?.items?.length || 0,
        website: businessInfo.name || '',
        order_no: orderDetails?.orderid || '',
        currency: businessInfo.currencycode || '',
        delivery_charges: !isZeroOrEmpty(orderDetails?.delivery_charges) ? roundToDecimal(orderDetails?.delivery_charges) : undefined,
        tip: roundToDecimal(orderDetails?.tip),
        service_charges: roundToDecimal(orderDetails?.service_charges),
        sub_total: roundToDecimal(orderDetails?.total),
        order_time: "Date: " + formatDateToAMPM(date),
        order_date: "Date: " + formatDateToAMPM(orderDetails?.delivery),
        subscription_id: scheduleId,
        order_text: "Cancelled",
        items: (orderDetails?.items || []).map((item) => ({
          text: item?.name || '',
          price: roundToDecimal(item?.price),
          count: item?.qty || 0,
          discount_check: (item?.discount || 0) == 0 ? false : true,
          dicount_value: `${roundToDecimal(item?.item_level_discount_value)}`,
          tax_check: !isZeroOrEmpty(item?.tax),
          tax: !isZeroOrEmpty(item?.tax) ? roundToDecimal(item?.tax) : undefined,
          tax_amount: !isZeroOrEmpty(item?.item_level_tax_value) ? 
            roundToDecimal(item?.item_level_tax_value) : undefined,
          weight_check: !isZeroOrEmpty(item?.weight_value),
          weight: !isZeroOrEmpty(item?.weight_value) ? 
            `${roundToDecimal(item?.weight_value)} ${item?.weight_unit || ''}` : undefined,
          orignal_price: roundToDecimal(item?.price),
          currency: businessInfo.currencycode || '',
        })),
      },
    };

    // Log the email message for debugging
    console.log('Prepared email message:', JSON.stringify(msg, null, 2));

    try {
      await sgMail.send(msg);
      console.log("Cancellation email sent successfully");
    } catch (sendGridError) {
      console.error("SendGrid Error:", {
        message: sendGridError.message,
        response: sendGridError.response?.body,
        code: sendGridError.code,
      });
      throw sendGridError;
    }
  } catch (error) {
    console.error("Detailed error in sendSubscriptionCancellationEmail:", {
      message: error.message,
      stack: error.stack,
      cause: error.cause,
    });
    throw error;
  }
};
