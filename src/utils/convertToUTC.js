exports.convertToUTC = (time, timeZone) => {
  // Get the sign, hours, and minutes from the timeZone
  const sign = timeZone[0];
  const hoursOffset = parseInt(timeZone.substring(1, 3), 10);
  const minutesOffset = parseInt(timeZone.substring(4, 6), 10);
  const totalOffsetMinutes =
    (sign === "+" ? -1 : 1) * (hoursOffset * 60 + minutesOffset);

  // Create a Date object for the time assuming it's in your local time
  const localDate = new Date(`1970-01-01T${time}`);

  // Convert local time to UTC
  const utcDate = new Date(
    localDate.getTime() +
      (localDate.getTimezoneOffset() + totalOffsetMinutes) * 60000
  );

  return utcDate.toISOString().substring(11, 19); // Get the time part in HH:MM:SS
};
