// Function to calculate the next execution time in seconds based on a weekly schedule
exports.calculateNextWeeklyTTL = (schedule, isFirstTimeHit) => {
  // Log the input parameters (schedule and whether it's the first time hit)
  console.log(
    "Params: ",
    "schedule: ",
    schedule,
    "isFirstTimeHit: ",
    isFirstTimeHit
  );

  // Get the current UTC date and time
  const currentUTCDate = new Date();

  // Get the current day of the week in UTC (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
  const currentDay = currentUTCDate.getUTCDay();

  // Calculate the current time in seconds (Unix timestamp)
  // If this is not the first time hit, add 5400 seconds (1.5 hours) to the current time
  const currentTime =
    Math.floor(Date.now() / 1000) + (isFirstTimeHit ? 0 : 5400);

  // Calculate the number of seconds passed since the start of the current day (in UTC)
  // This converts hours, minutes, and seconds to total seconds for the current day
  // Add 1.5 hours (5400 seconds) if it's not the first time hit
  const currentDayTimeInSeconds =
    currentUTCDate.getUTCHours() * 3600 + // Convert hours to seconds
    currentUTCDate.getUTCMinutes() * 60 + // Convert minutes to seconds
    currentUTCDate.getUTCSeconds() + // Add the current seconds
    (isFirstTimeHit ? 0 : 5400); // Add 1.5 hours if not first time hit

  // Define the number of seconds in a day (24 hours = 86400 seconds)
  const dayInSeconds = 86400;

  // Define the number of days in a week (7 days)
  const daysInWeek = 7;

  // Variable to store the calculated next execution time
  let nextExecutionTime = null;

  // Loop through the schedule object (which contains days of the week and their specific times)
  for (let day in schedule) {
    // Extract the specific time for the current day (e.g., "05:55:00")
    const specificTime = schedule[day];

    // Split the specific time into hours, minutes, and seconds, then convert them to numbers
    const [hours, minutes, seconds] = specificTime.split(":").map(Number);

    // Convert the specific time into seconds (hours * 3600 + minutes * 60 + seconds)
    const specificTimestamp = hours * 3600 + minutes * 60 + seconds;

    // Calculate the day difference between the current day and the scheduled day
    let dayDifference = day - currentDay;

    // If the scheduled day is earlier in the week (negative day difference), add 7 days to move to the next week
    if (dayDifference < 0) {
      dayDifference += daysInWeek;
    } else if (
      dayDifference === 0 &&
      specificTimestamp <= currentDayTimeInSeconds
    ) {
      // If it's the same day and the specific time has passed, move to the next occurrence of this day
      dayDifference += daysInWeek;
    }

    // Calculate the execution time based on the current time, day difference, and specific scheduled time
    const executionTime =
      currentTime +
      dayDifference * dayInSeconds +
      specificTimestamp -
      currentDayTimeInSeconds;

    // Update the next execution time if it's earlier than the previously found one
    if (!nextExecutionTime || executionTime < nextExecutionTime) {
      nextExecutionTime = executionTime;
    }
  }

  // Return the calculated next execution time, adjusted by subtracting 3600 seconds (1 hour)
  const newTTl = nextExecutionTime - 3600;

  // Log the calculated TTL for debugging purposes
  console.log("New TTL: ", newTTl);

  // Return the calculated TTL
  return newTTl;
};

// Function to calculate the next execution time in seconds for a bi-weekly schedule
exports.calculateNextBiWeeklyTTL = (schedule, isFirstTimeHit) => {
  console.log(
    "Params: ",
    "schedule: ",
    schedule,
    "isFirstTimeHit: ",
    isFirstTimeHit
  );

  // Get the current date and time in UTC
  const currentUTCDate = new Date();

  // Get the current UTC day of the week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
  const currentDay = currentUTCDate.getUTCDay();

  // Calculate the current Unix timestamp in seconds
  // If it's not the first time hit, add an additional 5400 seconds (1.5 hours)
  const currentTime =
    Math.floor(Date.now() / 1000) + (isFirstTimeHit ? 0 : 5400);

  // Calculate the number of seconds that have passed today (current time in seconds)
  // If it's not the first time hit, add 5400 seconds to the current time
  const currentDayTimeInSeconds =
    currentUTCDate.getUTCHours() * 3600 + // Convert hours to seconds
    currentUTCDate.getUTCMinutes() * 60 + // Convert minutes to seconds
    currentUTCDate.getUTCSeconds() + // Add the current seconds
    (isFirstTimeHit ? 0 : 5400); // Add 1.5 hours (if not the first time hit)

  // Define the number of seconds in a day (24 hours * 3600 seconds/hour)
  const dayInSeconds = 86400;

  // Define the number of days in a bi-week (14 days)
  const daysInBiWeek = 14;

  // Initialize a variable to store the next execution time in seconds
  let nextExecutionTime = null;

  // Loop through each day in the provided schedule
  for (let day in schedule) {
    // Get the scheduled time for the specific day (e.g., "05:55:00")
    const specificTime = schedule[day];

    // Split the time into hours, minutes, and seconds, and convert them to numbers
    const [hours, minutes, seconds] = specificTime.split(":").map(Number);

    // Convert the scheduled time into total seconds (hours * 3600 + minutes * 60 + seconds)
    const specificTimestamp = hours * 3600 + minutes * 60 + seconds;

    // Calculate the day difference between the scheduled day and the current day
    let dayDifference = day - currentDay;

    // If the scheduled day is in the past (negative day difference), move it to the next bi-week
    if (dayDifference < 0) {
      dayDifference += daysInBiWeek;
    }
    // If it's the same day but the scheduled time has already passed, move to the next bi-week occurrence
    else if (
      dayDifference === 0 &&
      specificTimestamp <= currentDayTimeInSeconds
    ) {
      dayDifference += daysInBiWeek;
    }

    // Calculate the execution time by adding the day difference and scheduled time
    // currentTime + day difference in seconds + scheduled time - current time in seconds for today
    const executionTime =
      currentTime +
      dayDifference * dayInSeconds +
      specificTimestamp -
      currentDayTimeInSeconds;

    // Update the next execution time if it's the earliest one found
    if (!nextExecutionTime || executionTime < nextExecutionTime) {
      nextExecutionTime = executionTime;
    }
  }

  // Return the calculated next execution time, adjusted by subtracting 3600 seconds (1 hour)
  const newTTl = nextExecutionTime - 3600;

  // Log the calculated TTL for debugging purposes
  console.log("New TTL: ", newTTl);

  // Return the calculated TTL
  return newTTl;
};

exports.calculateNextMonthlyTTL = (monthlySchedule) => {
  const currentDate = new Date();
  const currentTime = Math.floor(Date.now() / 1000);

  let nextExecutionTime = null;

  // Helper function to calculate execution time
  const calculateExecutionTime = (year, month, day, specificTimestamp) => {
    const executionDate = new Date(year, month, day);
    return Math.floor(executionDate.getTime() / 1000) + specificTimestamp;
  };

  for (let day in monthlySchedule) {
    const specificTime = monthlySchedule[day];
    const [hours, minutes, seconds] = specificTime.split(":").map(Number);
    const specificTimestamp = hours * 3600 + minutes * 60 + seconds;

    // Calculate execution time for the current month
    const executionTime = calculateExecutionTime(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day,
      specificTimestamp
    );

    if (
      executionTime > currentTime &&
      (!nextExecutionTime || executionTime < nextExecutionTime)
    ) {
      nextExecutionTime = executionTime;
    }
  }

  // If no valid execution time was found in the current month, check the next month
  if (!nextExecutionTime) {
    const nextMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() + 1,
      1
    );

    for (let day in monthlySchedule) {
      const specificTime = monthlySchedule[day];
      const [hours, minutes, seconds] = specificTime.split(":").map(Number);
      const specificTimestamp = hours * 3600 + minutes * 60 + seconds;

      const executionTime = calculateExecutionTime(
        nextMonth.getFullYear(),
        nextMonth.getMonth(),
        day,
        specificTimestamp
      );

      if (!nextExecutionTime || executionTime < nextExecutionTime) {
        nextExecutionTime = executionTime;
      }
    }
  }

  return nextExecutionTime;
};
