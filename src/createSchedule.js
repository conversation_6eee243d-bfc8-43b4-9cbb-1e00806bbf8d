const AWS = require('aws-sdk');

// Create a DynamoDB instance
const dynamoDB = new AWS.DynamoDB.DocumentClient();

// Your DynamoDB table name
const tableName = 'order-scheduler-orderTable-dev';

const insertOrder = async (event, context) => {
  try {

    const {orderId, order_status, business_id, cluster, type } = JSON.parse(event.body);
    const NOTIFY_TIME_IN_SECONDS = 2;
    const params = {
      TableName: tableName, // Replace with your DynamoDB table name
      Item: {
        orderId,
        order_status,
        business_id,
        cluster,
        type,
        ttl: Math.floor(Date.now() / 1000) + NOTIFY_TIME_IN_SECONDS, // Current Unix timestamp + 60 seconds (1 minute)
      },
    };

    await dynamoDB.put(params).promise();

    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'Item added successfully' }),
    };
  } catch (error) {
    console.log(error)
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal Server Error' }),
    };
  }
};

exports.handler = insertOrder
