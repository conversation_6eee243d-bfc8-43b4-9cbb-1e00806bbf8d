const AWS = require("aws-sdk");
const dynamoDB = new AWS.DynamoDB.DocumentClient();
const headers = {
  "Content-Type": "application/json",
  "Access-Control-Allow-Origin": "*", // or specify your allowed origin
  "Access-Control-Allow-Headers":
    "Origin, X-Requested-With, Content-Type, Accept",
  "Access-Control-Allow-Methods": "OPTIONS, POST, GET, PUT, DELETE",
};

const fetchSchedules = async (event) => {
  try {
    const tableName = process.env.DYNAMODB_SCHEDULE_TABLE;
    const businessId = event.pathParameters.businessId;
    const queryParams = event.queryStringParameters || {};

    if (!businessId) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          statusCode: 400,
          message: "businessId is required",
        }),
      };
    }

    // Query parameters for filtering
    const { dateFrom, dateTill, subscriptionStatus, scheduleType } =
      queryParams;

    const params = {
      TableName: tableName,
      IndexName: "businessId-scheduleId-index",
      KeyConditionExpression: "businessId = :businessId",
      ExpressionAttributeValues: {
        ":businessId": businessId,
      },
      ScanIndexForward: false,
    };

    // Add optional filters
    let filterExpressions = [];

    if (dateFrom) {
      filterExpressions.push("subscriptionTime >= :dateFrom");
      params.ExpressionAttributeValues[":dateFrom"] = dateFrom;
    }
    if (dateTill) {
      filterExpressions.push("subscriptionTime <= :dateTill");
      params.ExpressionAttributeValues[":dateTill"] = dateTill;
    }

    // Handle multi-selection for subscriptionStatus
    if (subscriptionStatus) {
      const statusValues = subscriptionStatus.split(",");

      filterExpressions.push(
        "#subscriptionStatus IN (" +
          statusValues
            .map((_, index) => `:subscriptionStatus${index}`)
            .join(", ") +
          ")"
      );
      statusValues.forEach((value, index) => {
        params.ExpressionAttributeValues[`:subscriptionStatus${index}`] = value;
      });
      params.ExpressionAttributeNames = {
        "#subscriptionStatus": "subscriptionStatus",
      };
    }

    // Handle multi-selection for scheduleType
    if (scheduleType) {
      const scheduleTypes = scheduleType.split(",");
      filterExpressions.push(
        "scheduleType IN (" +
          scheduleTypes.map((_, index) => `:scheduleType${index}`).join(", ") +
          ")"
      );
      scheduleTypes.forEach((type, index) => {
        params.ExpressionAttributeValues[`:scheduleType${index}`] = type;
      });
    }

    if (filterExpressions.length > 0) {
      params.FilterExpression = filterExpressions.join(" AND ");
    }

    let items = [];
    let data;

    do {
      data = await dynamoDB.query(params).promise();
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (typeof data.LastEvaluatedKey !== "undefined");
    // Sort items by scheduleId, converting scheduleId to a number for correct sorting
    items.sort((a, b) => parseInt(b.scheduleId) - parseInt(a.scheduleId));
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        statusCode: 200,
        message: "Entries fetched successfully",
        data: items,
      }),
    };
  } catch (error) {
    console.log("Error fetching entries:", error.message);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        statusCode: 500,
        message: "Internal Server Error",
        error: error.message,
      }),
    };
  }
};

const fetchUserSpecificSchedules = async (event) => {
  try {
    const tableName = process.env.DYNAMODB_SCHEDULE_TABLE;
    const businessId = event.pathParameters.businessId;
    const userId = event.pathParameters.userId; // Get userId from path parameters
    const queryParams = event.queryStringParameters || {};

    // Check if businessId and userId are provided
    if (!businessId) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          statusCode: 400,
          message: "businessId is required",
        }),
      };
    }

    if (!userId) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          statusCode: 400,
          message: "userId is required",
        }),
      };
    }

    // Query parameters for filtering
    const { dateFrom, dateTill, subscriptionStatus, scheduleType } =
      queryParams;

    const params = {
      TableName: tableName,
      IndexName: "businessId-scheduleId-index",
      KeyConditionExpression: "businessId = :businessId",
      FilterExpression: "userId = :userId", // Make userId filtering mandatory
      ExpressionAttributeValues: {
        ":businessId": businessId,
        ":userId": userId, // Add userId to the filter
      },
      ScanIndexForward: false,
    };

    // Add optional filters
    let filterExpressions = [];

    if (dateFrom) {
      filterExpressions.push("subscriptionTime >= :dateFrom");
      params.ExpressionAttributeValues[":dateFrom"] = dateFrom;
    }
    if (dateTill) {
      filterExpressions.push("subscriptionTime <= :dateTill");
      params.ExpressionAttributeValues[":dateTill"] = dateTill;
    }

    // Handle multi-selection for subscriptionStatus
    if (subscriptionStatus) {
      const statusValues = subscriptionStatus.split(",");

      filterExpressions.push(
        "#subscriptionStatus IN (" +
          statusValues
            .map((_, index) => `:subscriptionStatus${index}`)
            .join(", ") +
          ")"
      );
      statusValues.forEach((value, index) => {
        params.ExpressionAttributeValues[`:subscriptionStatus${index}`] = value;
      });
      params.ExpressionAttributeNames = {
        "#subscriptionStatus": "subscriptionStatus",
      };
    }

    // Handle multi-selection for scheduleType
    if (scheduleType) {
      const scheduleTypes = scheduleType.split(",");
      filterExpressions.push(
        "scheduleType IN (" +
          scheduleTypes.map((_, index) => `:scheduleType${index}`).join(", ") +
          ")"
      );
      scheduleTypes.forEach((type, index) => {
        params.ExpressionAttributeValues[`:scheduleType${index}`] = type;
      });
    }

    // Append filter expressions if there are any
    if (filterExpressions.length > 0) {
      params.FilterExpression += " AND " + filterExpressions.join(" AND ");
    }

    let items = [];
    let data;

    do {
      data = await dynamoDB.query(params).promise();
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (typeof data.LastEvaluatedKey !== "undefined");

    // Sort items by scheduleId, converting scheduleId to a number for correct sorting
    items.sort((a, b) => parseInt(b.scheduleId) - parseInt(a.scheduleId));

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        statusCode: 200,
        message: "Entries fetched successfully",
        data: items,
      }),
    };
  } catch (error) {
    console.log("Error fetching entries:", error.message);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        statusCode: 500,
        message: "Internal Server Error",
        error: error.message,
      }),
    };
  }
};
module.exports = { fetchSchedules, fetchUserSpecificSchedules };
