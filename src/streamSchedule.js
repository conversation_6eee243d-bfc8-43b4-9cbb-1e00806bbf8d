const axios = require("axios");
const AWS = require("aws-sdk");
const dynamoDB = new AWS.DynamoDB.DocumentClient();

const {
  calculateNextMonthlyTTL,
  calculateNextWeeklyTTL,
  calculateNextBiWeeklyTTL,
} = require("./utils/nextTTL");
const { convertToUTC } = require("./utils/convertToUTC");

function getCurrentTimeInTimezone(timeZoneOffset, newTime) {
  console.log(">>>>", { newTime, timeZoneOffset });
  // Get current time in UTC
  const now = new Date();

  // Extract hours and minutes from the timezone offset
  const sign = timeZoneOffset[0] === "-" ? -1 : 1;
  const hoursOffset = parseInt(timeZoneOffset.substring(1, 3));
  const minutesOffset = parseInt(timeZoneOffset.substring(4, 6));

  // Calculate the total offset in milliseconds
  const totalOffsetMillis =
    sign * ((hoursOffset * 60 + minutesOffset) * 60 * 1000);

  // Apply the offset to get the local time
  const localTime = new Date(now.getTime() + totalOffsetMillis);

  // Split the input time into hours, minutes, and seconds
  const [newHours, newMinutes, newSeconds] = newTime.split(":").map(Number);

  // Set the time of the localTime object to the new time
  localTime.setHours(newHours);
  localTime.setMinutes(newMinutes);
  localTime.setSeconds(newSeconds);

  // Format the date and time to "YYYY-MM-DD HH:mm:ss"
  const year = localTime.getFullYear();
  const month = String(localTime.getMonth() + 1).padStart(2, "0");
  const day = String(localTime.getDate()).padStart(2, "0");
  const hours = String(localTime.getHours()).padStart(2, "0");
  const minutes = String(localTime.getMinutes()).padStart(2, "0");
  const seconds = String(localTime.getSeconds()).padStart(2, "0");

  // Return the formatted date and time string
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

exports.handler = async (event) => {
  try {
    const tableName = process.env.DYNAMODB_SCHEDULE_TABLE;
    const postSchedules = [];
    const records = event.Records;

    for (const record of records) {
      if (record.eventName === "REMOVE") {
        const schedule = AWS.DynamoDB.Converter.unmarshall(
          record.dynamodb.OldImage
        );
        const timeZone = schedule.timeZone;
        const scheduleObj = schedule.schedule;
        const orderTime = scheduleObj[Object.keys(scheduleObj)[0]];
        const currentTime = getCurrentTimeInTimezone(timeZone, orderTime); // get order time
        console.log(
          "<<<current time>",
          currentTime,
          schedule.schedule,
          schedule
        );
        const orderDetails = JSON.parse(JSON.stringify(schedule.orderDetails));
        orderDetails.delivery_date = currentTime;
        const paymentDetails = schedule.paymentDetails;

        const ORDER_API_URL = `${process.env.ORDER_API_URL}/${schedule.businessId}/order`;

        console.log("URL>>", ORDER_API_URL, orderDetails);
        const response = await axios.post(ORDER_API_URL, orderDetails, {
          headers: { "Content-Type": "application/json" },
        });
        const orderInfo = {
          orderId: response?.data?.result?.orderid,
          orderTime: currentTime,
        };
        const ordersArrayInSchedule = [...schedule.orders, orderInfo];
        console.log(">>order array>>>>", ordersArrayInSchedule);

        const paymentData = {
          token: paymentDetails.cardId,
          order_id: response?.data?.result?.orderid,
          business_id: schedule.businessId,
          branch_id:
            response?.data?.result?.order_details[0]?.branch_id || "36",
          amount: response?.data?.result?.total,
          payment_type: "stripe",
          currency_code: response?.data?.result?.currency,
          name: response?.data?.result?.name,
          email: response?.data?.result?.email,
          customer_id: paymentDetails.customerId,
          user_id: paymentDetails.userId,
          save_card: false,
          pre_auth: "false",
          source: "app",
          return_url: "",
        };
        const TOKEN_API_URL = `${process.env.TOKEN_API_URL}/${schedule.businessId}/user/${paymentDetails.userId}/token`;
        let tokenResponse;
        try {
          tokenResponse = await axios.post(
            TOKEN_API_URL,
            {
              refresh_token: paymentDetails.refreshToken,
            },
            {
              headers: { "Content-Type": "application/json" },
            }
          );
        } catch (error) {
          console.log(error);
        }

        console.log(
          "Token APi Response>>>",
          tokenResponse?.data,
          ">>>>>>",
          "paymentData",
          paymentData
        );
        try {
          const PAYMENT_API_URL = `${process.env.PAYMENT_API_URL}/${schedule.businessId}/payment/stripe/charge`;

          const chargeApiResponse = await axios.post(
            PAYMENT_API_URL,
            paymentData,
            {
              headers: {
                "Content-Type": "application/json",
                Authorization: tokenResponse?.data?.result?.token,
              },
            }
          );
          console.log("Charge api response>>>>", chargeApiResponse, ">>>>>>>>");
        } catch (error) {
          console.log(error);
        }

        let nextExecutionTime = null;
        // Convert each time in the schedule to UTC
        const scheduleInUTC = {};
        for (const [day, time] of Object.entries(schedule.schedule)) {
          scheduleInUTC[day] = convertToUTC(time, timeZone);
        }

        if (schedule.scheduleType === "weekly") {
          nextExecutionTime = calculateNextWeeklyTTL(scheduleInUTC, false);
        } else if (schedule.scheduleType === "monthly") {
          nextExecutionTime = calculateNextMonthlyTTL(scheduleInUTC);
        } else if (schedule.scheduleType === "bi-weekly") {
          nextExecutionTime = calculateNextBiWeeklyTTL(scheduleInUTC, false);
          console.log(">>>>> scheduleInUTC ", scheduleInUTC);
          console.log(">>>>> newTTL ", nextExecutionTime);
        }
        console.log(">>>>> Next TTL outside", nextExecutionTime);
        console.log(
          ">>>>> Subscription Status>>>>>",
          schedule.subscriptionStatus,
          ">>>>"
        );

        if (schedule.subscriptionStatus === "inactive") {
          console.log(
            `>>>>>>>Schedule ${schedule.scheduleId} is marked as deleted. Skipping TTL update.`
          );
          // Don't set ttl but proceed with other operations
          schedule.orders = ordersArrayInSchedule;
          const params = {
            TableName: tableName,
            Item: schedule,
          };

          await dynamoDB.put(params).promise();
        } else if (nextExecutionTime) {
          console.log(
            ">>>>> Next TTL inside",
            nextExecutionTime,
            ">>>>>",
            schedule
          );
          schedule.ttl = nextExecutionTime;
          schedule.orders = ordersArrayInSchedule;
          const params = {
            TableName: tableName,
            Item: schedule,
          };

          await dynamoDB.put(params).promise();
        }
      }
    }

    await Promise.all(postSchedules);

    return {
      statusCode: 200,
      body: JSON.stringify({ message: "Event processed successfully" }),
    };
  } catch (error) {
    console.error("Error processing event:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: "Internal Server Error" }),
    };
  }
};
