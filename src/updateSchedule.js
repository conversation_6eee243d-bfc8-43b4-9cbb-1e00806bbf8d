const AWS = require("aws-sdk");
const dynamoDB = new AWS.DynamoDB.DocumentClient(); // Initialize DynamoDB Document Client

// Common headers used in API responses, including CORS settings
const headers = {
  "Content-Type": "application/json", // Set the content type to JSON
  "Access-Control-Allow-Origin": "*", // Allow requests from any origin (adjust as needed for security)
  "Access-Control-Allow-Headers":
    "Origin, X-Requested-With, Content-Type, Accept", // Specify allowed headers
  "Access-Control-Allow-Methods": "OPTIONS, POST, GET, PUT, DELETE", // Specify allowed HTTP methods
};

// Function to validate payment details, ensuring required fields are present and are of type string
const validatePaymentDetails = (paymentDetails) => {
  if (!paymentDetails) return false; // Return false if paymentDetails object is not provided

  const { cardId, customerId, refreshToken, userId } = paymentDetails; // Destructure payment details
  return (
    typeof cardId === "string" && // Validate that cardId is a string
    typeof customerId === "string" && // Validate that customerId is a string
    typeof refreshToken === "string" && // Validate that refreshToken is a string
    typeof userId === "string" // Validate that userId is a string
  );
};

// Lambda function to handle the update of a schedule in DynamoDB
const updateSchedule = async (event) => {
  try {
    // Parse the scheduleId and paymentDetails from the incoming request body
    let { scheduleId, paymentDetails } = JSON.parse(event.body);
    const tableName = process.env.DYNAMODB_SCHEDULE_TABLE; // Retrieve the DynamoDB table name from environment variables

    // Validate incoming paymentDetails object
    if (!validatePaymentDetails(paymentDetails)) {
      return {
        statusCode: 400, // Return a 400 Bad Request status if validation fails
        headers,
        body: JSON.stringify({
          statusCode: 400,
          error:
            "Invalid paymentDetails format. Ensure it includes cardId, customerId, refreshToken, and userId as strings.", // Error message for invalid format
        }),
      };
    }

    // Define the parameters for the DynamoDB update operation
    const params = {
      TableName: tableName, // Specify the table to update
      Key: { scheduleId }, // Specify the primary key (scheduleId) of the item to update
      UpdateExpression: "set paymentDetails = :paymentDetails", // Update the paymentDetails attribute
      ExpressionAttributeValues: {
        ":paymentDetails": paymentDetails, // Assign the new payment details to the update expression
      },
      ReturnValues: "ALL_NEW", // Return all attributes of the updated item
    };

    // Perform the update operation in DynamoDB
    const response = await dynamoDB.update(params).promise();

    // Return a success response with the updated attributes
    return {
      statusCode: 200, // HTTP 200 OK status
      headers,
      body: JSON.stringify({
        statusCode: 200,
        message: "Schedule updated successfully", // Success message
        updatedAttributes: response.Attributes, // Include the updated attributes in the response
      }),
    };
  } catch (error) {
    console.log(error); // Log the error for debugging purposes
    return {
      statusCode: 500, // Return a 500 Internal Server Error status if an exception occurs
      headers,
      body: JSON.stringify({ error: "Internal Server Error" }), // Generic error message
    };
  }
};

// Export the handler function for AWS Lambda
exports.handler = updateSchedule;
