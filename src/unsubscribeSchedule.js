const AWS = require("aws-sdk");
const dynamoDB = new AWS.DynamoDB.DocumentClient();
const { sendSubscriptionCancellationEmail } = require("./utils/email");

// Define common headers for the response, including CORS settings
const headers = {
  "Content-Type": "application/json",
  "Access-Control-Allow-Origin": "*", // Replace '*' with your specific origin if needed for security
  "Access-Control-Allow-Headers":
    "Origin, X-Requested-With, Content-Type, Accept",
  "Access-Control-Allow-Methods": "OPTIONS, POST, GET, PUT, DELETE", // Allowed HTTP methods
};

// The main function to handle the unsubscribe request
const unsubscribeSchedule = async (event) => {
  try {
    // Parse the schedule ID and cancellation reason from the incoming request body
    let { scheduleId, cancelationReason } = JSON.parse(event.body);
    const tableName = process.env.DYNAMODB_SCHEDULE_TABLE;

    // First, get the current schedule details
    const getParams = {
      TableName: tableName,
      Key: { scheduleId }
    };

    const scheduleData = await dynamoDB.get(getParams).promise();
    if (!scheduleData.Item) {
      return {
        statusCode: 404,
        headers,
        body: JSON.stringify({ error: "Schedule not found" }),
      };
    }

    // Update the schedule status
    const updateParams = {
      TableName: tableName,
      Key: { scheduleId },
      UpdateExpression: "set subscriptionStatus = :inactive, cancelationReason = :cancelationReason REMOVE #ttl",
      ExpressionAttributeNames: {
        "#ttl": "ttl",
      },
      ExpressionAttributeValues: {
        ":inactive": "inactive",
        ":cancelationReason": cancelationReason,
      },
      ReturnValues: "ALL_NEW"
    };

    const response = await dynamoDB.update(updateParams).promise();

    // Send cancellation email
    try {
      await sendSubscriptionCancellationEmail(
        scheduleData.Item.orderDetails,
        scheduleData.Item.businessId,
        scheduleId,
        cancelationReason
      );
      console.log("Cancellation email sent successfully");
    } catch (emailError) {
      console.error("Email error details:", {
        message: emailError.message,
        cause: emailError.cause,
        response: emailError.response?.body,
      });
      // Continue execution even if email fails
    }

    // Return a success response with the updated attributes
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        statusCode: 200,
        message: "Schedule unsubscribed successfully",
        updatedAttributes: response.Attributes, // Include the updated attributes in the response
      }),
    };
  } catch (error) {
    // Log the error and return a generic internal server error response
    console.log(error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: "Internal Server Error" }),
    };
  }
};

// Export the handler function for AWS Lambda
exports.handler = unsubscribeSchedule;
