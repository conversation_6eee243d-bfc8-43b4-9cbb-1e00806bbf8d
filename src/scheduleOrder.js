const AWS = require("aws-sdk");
const dynamoDB = new AWS.DynamoDB.DocumentClient();
const dynamoDBClient = new AWS.DynamoDB(); // Use this client to manage tables

const headers = {
  "Content-Type": "application/json",
  "Access-Control-Allow-Origin": "*", // or specify your allowed origin
  "Access-Control-Allow-Headers":
    "Origin, X-Requested-With, Content-Type, Accept",
  "Access-Control-Allow-Methods": "OPTIONS, POST, GET, PUT, DELETE",
};

const tableName = process.env.DYNAMODB_SCHEDULE_TABLE;
const autoIncrementTableName = process.env.DYNAMODB_AUTOINCREMENT_TABLE;
console.log("eeeeeeeeeeeeeeeesss>>", tableName, autoIncrementTableName);
const counterKeyName = "scheduleIdCounter";

const {
  calculateNextMonthlyTTL,
  calculateNextWeeklyTTL,
  calculateNextBiWeeklyTTL,
} = require("./utils/nextTTL");
const { convertToUTC } = require("./utils/convertToUTC");
const { sendSubscriptionConfirmationEmail } = require("./utils/email");

const isValidISODate = (dateString) => {
  const iso8601Regex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/;
  return iso8601Regex.test(dateString);
};

function getCurrentTimeInTimeZone(offset) {
  // Get current time in UTC
  const now = new Date();

  // Convert offset to minutes
  const offsetMinutes =
    parseInt(offset.substring(1, 3)) * 60 + parseInt(offset.substring(4, 6));

  // If offset is negative, negate the offsetMinutes
  const totalOffsetMinutes = offset[0] === "-" ? -offsetMinutes : offsetMinutes;

  // Get the current time in the specified time zone
  const localTime = new Date(
    now.getTime() + totalOffsetMinutes * 60000
  ).toISOString();
  // Convert the ISO string to a Date object
  const date = new Date(localTime);
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, "0"); // Months are zero-indexed
  const day = String(date.getUTCDate()).padStart(2, "0");
  const hours = String(date.getUTCHours()).padStart(2, "0");
  const minutes = String(date.getUTCMinutes()).padStart(2, "0");
  const seconds = String(date.getUTCSeconds()).padStart(2, "0");

  // Format the date and time as "YYYY-MM-DD HH:MM:SS"
  const formattedDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  return { formattedDateTime, localTime };
}
const createAutoIncrementTable = async () => {
  const params = {
    TableName: autoIncrementTableName,
    KeySchema: [{ AttributeName: "idName", KeyType: "HASH" }],
    AttributeDefinitions: [{ AttributeName: "idName", AttributeType: "S" }],
    ProvisionedThroughput: {
      ReadCapacityUnits: 5,
      WriteCapacityUnits: 5,
    },
  };

  await dynamoDBClient.createTable(params).promise();

  // Initialize the counter with a value of 1
  const initParams = {
    TableName: autoIncrementTableName,
    Item: {
      idName: counterKeyName,
      currentValue: 1,
    },
  };

  await dynamoDB.put(initParams).promise();
};

const ensureTableExists = async () => {
  try {
    await dynamoDBClient
      .describeTable({ TableName: autoIncrementTableName })
      .promise();
  } catch (err) {
    if (err.code === "ResourceNotFoundException") {
      // Table doesn't exist, create it
      await createAutoIncrementTable();
    } else {
      throw err; // Some other error occurred
    }
  }
};

const getNextScheduleId = async () => {
  const params = {
    TableName: autoIncrementTableName,
    Key: { idName: counterKeyName },
    UpdateExpression:
      "set currentValue = if_not_exists(currentValue, :start) + :increment",
    ExpressionAttributeValues: {
      ":increment": 1,
      ":start": 1, // Start value if currentValue doesn't exist
    },
    ReturnValues: "UPDATED_NEW",
  };

  const result = await dynamoDB.update(params).promise();
  return result.Attributes.currentValue;
};

const insertSchedule = async (event) => {
  try {
    // Ensure that the auto-increment table exists and is initialized
    await ensureTableExists();

    let {
      scheduleType,
      schedule,
      orderDetails,
      businessId,
      timeZone,
      paymentDetails,
    } = JSON.parse(event.body);

    // Convert each time in the schedule to UTC
    const scheduleInUTC = {};
    for (const [day, time] of Object.entries(schedule)) {
      scheduleInUTC[day] = convertToUTC(time, timeZone);
    }

    const scheduleId = (await getNextScheduleId()).toString();

    orderDetails.subscription = {
      subscription_id: scheduleId,
      subscription_type: scheduleType,
    };

    let ttl = null;
    if (scheduleType === "one-time") {
      if (!isValidISODate(schedule)) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({
            statusCode: 400,
            error: "Wrong data in 'schedule'",
          }),
        };
      }
      ttl = new Date(scheduleInUTC).getTime() / 1000;
    } else if (scheduleType === "weekly") {
      ttl = calculateNextWeeklyTTL(scheduleInUTC, true);
    } else if (scheduleType === "monthly") {
      ttl = calculateNextMonthlyTTL(scheduleInUTC);
    } else if (scheduleType === "bi-weekly") {
      ttl = calculateNextBiWeeklyTTL(scheduleInUTC, true);
    }
    const currentTimeInTimeZone = getCurrentTimeInTimeZone(timeZone);
    console.log(">>>>>>sub-time>>>>", currentTimeInTimeZone);
    let params = {
      TableName: tableName,
      Item: {
        scheduleId,
        subscriptionStatus: "active",
        orders: [],
        userId: paymentDetails.userId,
        cancelationReason: null,
        scheduleType,
        schedule,
        orderDetails,
        subscriptionTime: currentTimeInTimeZone.formattedDateTime,
        ttl,
        businessId,
        timeZone,
        paymentDetails,
      },
    };
    const response = await dynamoDB.put(params).promise();
    console.log("PARAMS>>>>>>>>>>>>", params, "res>>", response);

    // Send the confirmation email
    await sendSubscriptionConfirmationEmail(
      orderDetails, // pass the order details
      businessId,
      scheduleId,
      currentTimeInTimeZone.localTime
    );

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        statusCode: 200,
        message: "Schedule added successfully",
        scheduleId,
      }),
    };
  } catch (error) {
    console.log(error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ statusCode: 500, error: "Internal Server Error" }),
    };
  }
};

exports.handler = insertSchedule;
